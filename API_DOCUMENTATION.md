# Admin Panel API Documentation

This document provides comprehensive information about the Admin Panel API, including how to access the interactive Swagger documentation and use the various endpoints.

## 🚀 Quick Start

### Accessing the API Documentation

1. **Start the development server:**
   ```bash
   npm run dev
   ```

2. **Open the interactive Swagger UI:**
   - Navigate to: `http://localhost:3000/api/docs`
   - This provides a fully interactive API documentation interface

3. **Access the raw OpenAPI specification:**
   - JSON format: `http://localhost:3000/api/docs/swagger.json`

## 📚 API Overview

The API is organized into the following main categories:

### 🔐 Authentication
- **POST** `/api/auth` - User login
- **POST** `/api/auth/logout` - User logout
- **POST** `/api/auth/refresh` - Refresh access token

### 👥 User Management
- **GET** `/api/users/subscribers` - Get all subscribers (paginated)
- **POST** `/api/users/subscribers` - Create new subscriber
- **GET** `/api/users/subscribers/{id}` - Get subscriber by ID
- **PUT** `/api/users/subscribers/{id}` - Update subscriber
- **DELETE** `/api/users/subscribers/{id}` - Delete subscriber

### 📖 Content Management
- **GET** `/api/content/tracks` - Get all learning tracks
- **POST** `/api/content/tracks` - Create new track

### 📊 Dashboard
- **GET** `/api/dashboard/stats` - Get dashboard statistics

### 📁 File Upload
- **GET** `/api/upload` - Get upload guidelines
- **POST** `/api/upload` - Upload files (images, documents, etc.)

## 🔑 Authentication

Most API endpoints require authentication. The API supports JWT Bearer token authentication:

```http
Authorization: Bearer <your-jwt-token>
```

### Getting Started with Authentication

1. **Login to get a token:**
   ```bash
   curl -X POST http://localhost:3000/api/auth \
     -H "Content-Type: application/json" \
     -d '{
       "email": "<EMAIL>",
       "password": "password123"
     }'
   ```

2. **Use the token in subsequent requests:**
   ```bash
   curl -X GET http://localhost:3000/api/users/subscribers \
     -H "Authorization: Bearer <your-token>"
   ```

## 📋 API Features

### ✅ Comprehensive Documentation
- **Interactive Swagger UI** with try-it-out functionality
- **Detailed request/response schemas** for all endpoints
- **Authentication examples** and security requirements
- **Error response documentation** with proper HTTP status codes

### 🔍 Advanced Filtering & Pagination
- **Pagination support** on list endpoints (page, limit)
- **Search functionality** across relevant fields
- **Filtering options** by status, role, type, etc.
- **Sorting capabilities** with configurable order

### 📤 File Upload Support
- **Multiple file types** supported (images, documents, videos, audio)
- **File size validation** (10MB limit)
- **MIME type validation** for security
- **Categorized uploads** for different purposes

### 🛡️ Robust Error Handling
- **Consistent error response format** across all endpoints
- **Proper HTTP status codes** (400, 401, 404, 409, 500, etc.)
- **Detailed error messages** for debugging
- **Input validation** with specific field-level errors

## 🏗️ API Structure

### Response Format
All API responses follow a consistent structure:

```json
{
  "success": true,
  "message": "Operation completed successfully",
  "data": { /* response data */ },
  "meta": { /* pagination/metadata */ }
}
```

### Error Format
Error responses use the same structure:

```json
{
  "success": false,
  "message": "Error description",
  "error": "Detailed error information",
  "statusCode": 400
}
```

### Pagination Format
Paginated responses include metadata:

```json
{
  "success": true,
  "message": "Data retrieved successfully",
  "data": [ /* array of items */ ],
  "meta": {
    "page": 1,
    "limit": 10,
    "total": 100,
    "totalPages": 10
  }
}
```

## 🧪 Testing the API

### Using the Swagger UI
1. Open `http://localhost:3000/api/docs`
2. Click on any endpoint to expand it
3. Click "Try it out" to test the endpoint
4. Fill in the required parameters
5. Click "Execute" to make the request

### Using curl
```bash
# Get all subscribers
curl -X GET "http://localhost:3000/api/users/subscribers?page=1&limit=10" \
  -H "Authorization: Bearer <token>"

# Create a new subscriber
curl -X POST "http://localhost:3000/api/users/subscribers" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "name": "John Doe",
    "email": "<EMAIL>",
    "status": "active",
    "role": "viewer"
  }'
```

### Using Postman
1. Import the OpenAPI specification from `http://localhost:3000/api/docs/swagger.json`
2. Set up authentication with Bearer token
3. Test endpoints directly from Postman

## 🔧 Development

### Adding New Endpoints
1. Create the route file in `src/app/api/`
2. Add Swagger documentation comments
3. Follow the established patterns for:
   - Request/response schemas
   - Error handling
   - Authentication
   - Validation

### Updating Documentation
The Swagger documentation is automatically generated from JSDoc comments in the API route files. To update:

1. Modify the `@swagger` comments in route files
2. Add new schemas to the swagger configuration
3. Test with `node scripts/test-swagger.js`

## 📞 Support

For questions or issues with the API:
- Check the interactive documentation at `/api/docs`
- Review the error messages for specific guidance
- Ensure proper authentication headers are included
- Validate request data against the provided schemas

## 🚀 Production Deployment

Before deploying to production:
1. Update server URLs in `src/lib/swagger.ts`
2. Implement proper JWT token validation
3. Add rate limiting and security middleware
4. Set up proper file storage (AWS S3, etc.)
5. Configure database connections
6. Add monitoring and logging
