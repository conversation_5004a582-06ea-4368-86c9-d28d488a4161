// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  error?: string;
  statusCode?: number;
}

export interface PaginatedResponse<T = any> extends ApiResponse<T[]> {
  meta: PaginationMeta;
}

export interface PaginationMeta {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

// Content Management Types
export interface Track {
  id: number;
  title: string;
  image: string;
  description?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Subject {
  id: number;
  name: string;
  color: string;
  trackId: number;
  createdAt: string;
  updatedAt: string;
}

export interface Chapter {
  id: number;
  title: string;
  pages: number;
  image: string;
  subjectId: number;
  createdAt: string;
  updatedAt: string;
}

export interface ContentPage {
  id: string;
  title: string;
  content: string;
  chapterId: number;
  order: number;
  createdAt: string;
  updatedAt: string;
}

// User Management Types
export interface User {
  id: string;
  name: string;
  email: string;
  phone?: string;
  avatar?: string;
  joinedDate: string;
  lastActive: string;
  status: 'Active' | 'Inactive' | 'Suspended';
  plan?: string;
}

export interface TeamMember extends User {
  role: 'admin' | 'editor' | 'viewer' | 'content';
  permissions: string[];
  createdContent?: number;
}

// Finance Types
export interface Transaction {
  id: string;
  userId: string;
  amount: number;
  currency: string;
  type: 'payment' | 'refund' | 'subscription' | 'prize';
  status: 'pending' | 'completed' | 'failed' | 'cancelled';
  description: string;
  createdAt: string;
  updatedAt: string;
}

export interface FinancialSummary {
  totalRevenue: number;
  monthlyRevenue: number;
  totalTransactions: number;
  pendingPayments: number;
  refundAmount: number;
  currency: string;
  period: string;
}

// Sponsor & Advertisement Types
export interface Sponsor {
  id: string;
  name: string;
  logo: string;
  website?: string;
  contactEmail: string;
  contactPhone?: string;
  status: 'active' | 'inactive';
  totalSponsorship: number;
  createdAt: string;
  updatedAt: string;
}

export interface Advertisement {
  id: string;
  title: string;
  description: string;
  image: string;
  url?: string;
  sponsorId: string;
  type: 'banner' | 'popup' | 'inline' | 'video';
  placement: 'header' | 'sidebar' | 'content' | 'footer';
  status: 'active' | 'inactive' | 'scheduled';
  startDate: string;
  endDate: string;
  impressions: number;
  clicks: number;
  createdAt: string;
  updatedAt: string;
}

// Test Results Types
export interface TestResult {
  id: number;
  testName: string;
  date: string;
  sponsor: string;
  scholars: number;
  testType: 'weekly' | 'monthly' | 'quarterly';
  status: 'published' | 'unpublished' | 'draft';
  createdAt: string;
  updatedAt: string;
}

export interface PrizeCategory {
  id: string;
  prizeRange: string;
  sponsor: string;
  prizeType: string;
  prizeValue: string;
  document: string;
  testResultId: number;
  createdAt: string;
  updatedAt: string;
}

// Master Data Types
export interface TimeTable {
  id: string;
  title: string;
  description: string;
  startTime: string;
  endTime: string;
  dayOfWeek: number; // 0-6 (Sunday-Saturday)
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface Prize {
  id: string;
  name: string;
  description: string;
  value: number;
  currency: string;
  type: 'cash' | 'voucher' | 'product' | 'scholarship';
  sponsorId: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

// Dashboard Types
export interface DashboardStats {
  totalSubscribers: number;
  activeSubscribers: number;
  totalContent: number;
  totalRevenue: number;
  monthlyGrowth: {
    subscribers: number;
    revenue: number;
  };
  recentActivity: ActivityLog[];
}

export interface ActivityLog {
  id: string;
  type: 'user_joined' | 'content_created' | 'payment_received' | 'test_completed' | 'system_update';
  description: string;
  timestamp: string;
  user?: {
    id: string;
    name: string;
    email: string;
  };
  metadata?: Record<string, any>;
}

// Audit Log Types
export interface AuditLog {
  id: string;
  userId: string;
  action: string;
  resource: string;
  resourceId: string;
  oldValues?: Record<string, any>;
  newValues?: Record<string, any>;
  ipAddress: string;
  userAgent: string;
  timestamp: string;
}

// Request Types
export interface CreateTrackRequest {
  title: string;
  image: string;
  description?: string;
}

export interface UpdateTrackRequest {
  title?: string;
  image?: string;
  description?: string;
}

export interface CreateSubjectRequest {
  name: string;
  color: string;
  trackId: number;
}

export interface CreateChapterRequest {
  title: string;
  image: string;
  subjectId: number;
}

export interface CreateUserRequest {
  name: string;
  email: string;
  phone?: string;
  role: 'admin' | 'editor' | 'viewer' | 'content';
  status: 'active' | 'inactive' | 'pending';
}

export interface UpdateUserRequest {
  name?: string;
  email?: string;
  phone?: string;
  role?: 'admin' | 'editor' | 'viewer' | 'content';
  status?: 'active' | 'inactive' | 'pending';
}

// File Upload Types
export interface FileUploadResponse {
  success: boolean;
  message: string;
  data: {
    filename: string;
    originalName: string;
    size: number;
    mimeType: string;
    url: string;
    uploadedAt: string;
  };
}

// Search and Filter Types
export interface SearchFilters {
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  dateFrom?: string;
  dateTo?: string;
}

export interface UserFilters extends SearchFilters {
  status?: 'active' | 'inactive' | 'pending';
  role?: 'admin' | 'editor' | 'viewer' | 'content';
}

export interface ContentFilters extends SearchFilters {
  trackId?: number;
  subjectId?: number;
  type?: 'track' | 'subject' | 'chapter' | 'page';
}
