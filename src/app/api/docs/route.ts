import { NextRequest, NextResponse } from "next/server";
import { swaggerSpec } from "@/lib/swagger";

/**
 * @swagger
 * /api/docs:
 *   get:
 *     summary: Swagger API Documentation
 *     description: Interactive API documentation using Swagger UI
 *     tags: [Documentation]
 *     responses:
 *       200:
 *         description: Swagger UI HTML page
 *         content:
 *           text/html:
 *             schema:
 *               type: string
 */
export async function GET(request: NextRequest) {
  try {
    // Generate Swagger UI HTML manually for Next.js compatibility
    const swaggerUiHtml = `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Admin Panel API Documentation</title>
  <link rel="stylesheet" type="text/css" href="https://unpkg.com/swagger-ui-dist@5.9.0/swagger-ui.css" />
  <link rel="icon" type="image/png" href="/favicon.ico" />
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <style>
    /* Dark Theme Variables */
    :root {
      --bg-primary: #0f0f23;
      --bg-secondary: #1a1a2e;
      --bg-tertiary: #16213e;
      --text-primary: #e2e8f0;
      --text-secondary: #94a3b8;
      --accent-primary: #3b82f6;
      --accent-secondary: #10b981;
      --accent-danger: #ef4444;
      --accent-warning: #f59e0b;
      --border-color: #334155;
      --shadow-color: rgba(0, 0, 0, 0.5);
    }

    /* Base Styles */
    html {
      box-sizing: border-box;
      overflow: -moz-scrollbars-vertical;
      overflow-y: scroll;
    }

    *, *:before, *:after {
      box-sizing: inherit;
    }

    body {
      margin: 0;
      background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
      min-height: 100vh;
      position: relative;
      overflow-x: hidden;
    }

    /* Animated Background */
    body::before {
      content: '';
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background:
        radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(16, 185, 129, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(139, 92, 246, 0.1) 0%, transparent 50%);
      animation: backgroundShift 20s ease-in-out infinite;
      z-index: -1;
    }

    @keyframes backgroundShift {
      0%, 100% { transform: translateX(0) translateY(0); }
      25% { transform: translateX(-20px) translateY(-10px); }
      50% { transform: translateX(20px) translateY(10px); }
      75% { transform: translateX(-10px) translateY(20px); }
    }

    /* Loading Animation */
    .loading-container {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: var(--bg-primary);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      z-index: 9999;
      transition: opacity 0.5s ease-out, visibility 0.5s ease-out;
    }

    .loading-container.hidden {
      opacity: 0;
      visibility: hidden;
    }

    .loading-spinner {
      width: 60px;
      height: 60px;
      border: 3px solid var(--border-color);
      border-top: 3px solid var(--accent-primary);
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .loading-text {
      margin-top: 20px;
      color: var(--text-secondary);
      font-size: 14px;
      animation: pulse 2s ease-in-out infinite;
    }

    @keyframes pulse {
      0%, 100% { opacity: 0.6; }
      50% { opacity: 1; }
    }

    /* Custom Header */
    .custom-header {
      background: rgba(26, 26, 46, 0.95);
      backdrop-filter: blur(10px);
      border-bottom: 1px solid var(--border-color);
      padding: 20px 0;
      position: sticky;
      top: 0;
      z-index: 1000;
      animation: slideDown 0.8s ease-out;
    }

    @keyframes slideDown {
      from { transform: translateY(-100%); opacity: 0; }
      to { transform: translateY(0); opacity: 1; }
    }

    .header-content {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 20px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .header-title {
      display: flex;
      align-items: center;
      gap: 15px;
    }

    .api-icon {
      width: 40px;
      height: 40px;
      background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
      border-radius: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 20px;
      animation: iconFloat 3s ease-in-out infinite;
    }

    @keyframes iconFloat {
      0%, 100% { transform: translateY(0px); }
      50% { transform: translateY(-5px); }
    }

    .header-title h1 {
      color: var(--text-primary);
      font-size: 24px;
      font-weight: 600;
      margin: 0;
      background: linear-gradient(135deg, var(--text-primary), var(--accent-primary));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .header-subtitle {
      color: var(--text-secondary);
      font-size: 14px;
      margin: 5px 0 0 0;
    }
  </style>
</head>
<body>
  <!-- Loading Screen -->
  <div class="loading-container" id="loading">
    <div class="loading-spinner"></div>
    <div class="loading-text">Loading API Documentation...</div>
  </div>

  <!-- Custom Header -->
  <div class="custom-header">
    <div class="header-content">
      <div class="header-title">
        <div class="api-icon">🚀</div>
        <div>
          <h1>Admin Panel API Gateway</h1>
          <p class="header-subtitle">Interactive API Documentation with Spring Boot Integration</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Swagger UI Container -->
  <div id="swagger-ui"></div>

  <script src="https://unpkg.com/swagger-ui-dist@5.9.0/swagger-ui-bundle.js"></script>
  <script src="https://unpkg.com/swagger-ui-dist@5.9.0/swagger-ui-standalone-preset.js"></script>
  <script>
    // Hide loading screen after page loads
    function hideLoading() {
      const loading = document.getElementById('loading');
      if (loading) {
        loading.classList.add('hidden');
        setTimeout(() => {
          loading.style.display = 'none';
        }, 500);
      }
    }

    // Apply dark theme styles to Swagger UI
    function applyDarkTheme() {
      const style = document.createElement('style');
      style.textContent = \`
        /* Swagger UI Dark Theme Overrides */
        .swagger-ui {
          color: var(--text-primary) !important;
          font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif !important;
        }

        .swagger-ui .topbar { display: none !important; }

        /* Main container */
        .swagger-ui .wrapper {
          max-width: 1200px;
          margin: 0 auto;
          padding: 20px;
          animation: fadeInUp 1s ease-out 0.3s both;
        }

        @keyframes fadeInUp {
          from { opacity: 0; transform: translateY(30px); }
          to { opacity: 1; transform: translateY(0); }
        }

        /* Info section */
        .swagger-ui .info {
          background: rgba(26, 26, 46, 0.8) !important;
          border: 1px solid var(--border-color) !important;
          border-radius: 12px !important;
          padding: 30px !important;
          margin: 20px 0 !important;
          backdrop-filter: blur(10px) !important;
          animation: slideInLeft 0.8s ease-out;
        }

        @keyframes slideInLeft {
          from { opacity: 0; transform: translateX(-50px); }
          to { opacity: 1; transform: translateX(0); }
        }

        .swagger-ui .info .title {
          color: var(--text-primary) !important;
          font-size: 32px !important;
          font-weight: 700 !important;
          margin-bottom: 10px !important;
          background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary)) !important;
          -webkit-background-clip: text !important;
          -webkit-text-fill-color: transparent !important;
          background-clip: text !important;
        }

        .swagger-ui .info .description {
          color: var(--text-secondary) !important;
          font-size: 16px !important;
          line-height: 1.6 !important;
        }

        .swagger-ui .info .description p {
          color: var(--text-secondary) !important;
        }

        .swagger-ui .info .description h1,
        .swagger-ui .info .description h2,
        .swagger-ui .info .description h3 {
          color: var(--text-primary) !important;
        }

        /* Operation blocks */
        .swagger-ui .opblock {
          background: rgba(26, 26, 46, 0.6) !important;
          border: 1px solid var(--border-color) !important;
          border-radius: 8px !important;
          margin: 15px 0 !important;
          backdrop-filter: blur(5px) !important;
          transition: all 0.3s ease !important;
          animation: slideInRight 0.6s ease-out;
        }

        @keyframes slideInRight {
          from { opacity: 0; transform: translateX(50px); }
          to { opacity: 1; transform: translateX(0); }
        }

        .swagger-ui .opblock:hover {
          transform: translateY(-2px) !important;
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3) !important;
          border-color: var(--accent-primary) !important;
        }

        .swagger-ui .opblock.opblock-post {
          border-left: 4px solid var(--accent-secondary) !important;
        }

        .swagger-ui .opblock.opblock-get {
          border-left: 4px solid var(--accent-primary) !important;
        }

        .swagger-ui .opblock.opblock-put {
          border-left: 4px solid var(--accent-warning) !important;
        }

        .swagger-ui .opblock.opblock-delete {
          border-left: 4px solid var(--accent-danger) !important;
        }

        .swagger-ui .opblock .opblock-summary {
          background: transparent !important;
          border: none !important;
          color: var(--text-primary) !important;
          padding: 20px !important;
        }

        .swagger-ui .opblock .opblock-summary-description {
          color: var(--text-secondary) !important;
        }

        .swagger-ui .opblock .opblock-summary-path {
          color: var(--text-primary) !important;
          font-family: 'Monaco', 'Menlo', monospace !important;
          background: rgba(59, 130, 246, 0.1) !important;
          padding: 4px 8px !important;
          border-radius: 4px !important;
          border: 1px solid rgba(59, 130, 246, 0.3) !important;
        }

        /* Method badges */
        .swagger-ui .opblock .opblock-summary-method {
          border-radius: 6px !important;
          font-weight: 600 !important;
          text-transform: uppercase !important;
          font-size: 12px !important;
          padding: 6px 12px !important;
          min-width: 60px !important;
          text-align: center !important;
        }

        /* Expanded content */
        .swagger-ui .opblock-body {
          background: rgba(15, 15, 35, 0.8) !important;
          border-top: 1px solid var(--border-color) !important;
          color: var(--text-primary) !important;
        }

        .swagger-ui .parameters-container {
          background: transparent !important;
          padding: 20px !important;
        }

        .swagger-ui .parameter__name {
          color: var(--accent-primary) !important;
          font-weight: 600 !important;
        }

        .swagger-ui .parameter__type {
          color: var(--accent-secondary) !important;
        }

        .swagger-ui .parameter__description {
          color: var(--text-secondary) !important;
        }

        /* Response section */
        .swagger-ui .responses-wrapper {
          background: transparent !important;
          padding: 20px !important;
        }

        .swagger-ui .response {
          background: rgba(26, 26, 46, 0.6) !important;
          border: 1px solid var(--border-color) !important;
          border-radius: 6px !important;
          margin: 10px 0 !important;
        }

        .swagger-ui .response .response-col_status {
          color: var(--accent-secondary) !important;
          font-weight: 600 !important;
        }

        .swagger-ui .response .response-col_description {
          color: var(--text-secondary) !important;
        }

        /* Try it out button */
        .swagger-ui .btn.try-out__btn {
          background: var(--accent-primary) !important;
          color: white !important;
          border: none !important;
          border-radius: 6px !important;
          padding: 8px 16px !important;
          font-weight: 500 !important;
          transition: all 0.3s ease !important;
        }

        .swagger-ui .btn.try-out__btn:hover {
          background: #2563eb !important;
          transform: translateY(-1px) !important;
          box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3) !important;
        }

        /* Execute button */
        .swagger-ui .btn.execute {
          background: var(--accent-secondary) !important;
          color: white !important;
          border: none !important;
          border-radius: 6px !important;
          padding: 10px 20px !important;
          font-weight: 600 !important;
          transition: all 0.3s ease !important;
        }

        .swagger-ui .btn.execute:hover {
          background: #059669 !important;
          transform: translateY(-1px) !important;
          box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3) !important;
        }

        /* Input fields */
        .swagger-ui input[type="text"],
        .swagger-ui input[type="password"],
        .swagger-ui input[type="email"],
        .swagger-ui textarea,
        .swagger-ui select {
          background: rgba(15, 15, 35, 0.8) !important;
          border: 1px solid var(--border-color) !important;
          border-radius: 6px !important;
          color: var(--text-primary) !important;
          padding: 10px !important;
          transition: all 0.3s ease !important;
        }

        .swagger-ui input:focus,
        .swagger-ui textarea:focus,
        .swagger-ui select:focus {
          border-color: var(--accent-primary) !important;
          box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2) !important;
          outline: none !important;
        }

        /* Code blocks */
        .swagger-ui .highlight-code {
          background: rgba(15, 15, 35, 0.9) !important;
          border: 1px solid var(--border-color) !important;
          border-radius: 6px !important;
          color: var(--text-primary) !important;
        }

        .swagger-ui .microlight {
          color: var(--text-primary) !important;
        }

        /* Scrollbars */
        .swagger-ui ::-webkit-scrollbar {
          width: 8px;
          height: 8px;
        }

        .swagger-ui ::-webkit-scrollbar-track {
          background: var(--bg-secondary);
          border-radius: 4px;
        }

        .swagger-ui ::-webkit-scrollbar-thumb {
          background: var(--border-color);
          border-radius: 4px;
        }

        .swagger-ui ::-webkit-scrollbar-thumb:hover {
          background: var(--accent-primary);
        }

        /* Tags */
        .swagger-ui .opblock-tag {
          background: rgba(26, 26, 46, 0.8) !important;
          border: 1px solid var(--border-color) !important;
          border-radius: 8px !important;
          margin: 20px 0 !important;
          backdrop-filter: blur(10px) !important;
          animation: fadeIn 0.8s ease-out;
        }

        @keyframes fadeIn {
          from { opacity: 0; }
          to { opacity: 1; }
        }

        .swagger-ui .opblock-tag-section h3 {
          color: var(--text-primary) !important;
          font-size: 20px !important;
          font-weight: 600 !important;
          padding: 20px !important;
          margin: 0 !important;
          background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary)) !important;
          -webkit-background-clip: text !important;
          -webkit-text-fill-color: transparent !important;
          background-clip: text !important;
        }

        /* Models section */
        .swagger-ui .model-container {
          background: rgba(26, 26, 46, 0.6) !important;
          border: 1px solid var(--border-color) !important;
          border-radius: 8px !important;
          margin: 10px 0 !important;
        }

        .swagger-ui .model .property {
          color: var(--text-primary) !important;
        }

        .swagger-ui .model .property-type {
          color: var(--accent-secondary) !important;
        }

        /* Authorization */
        .swagger-ui .auth-wrapper {
          background: rgba(26, 26, 46, 0.8) !important;
          border: 1px solid var(--border-color) !important;
          border-radius: 8px !important;
          backdrop-filter: blur(10px) !important;
        }

        .swagger-ui .auth-container h4 {
          color: var(--text-primary) !important;
        }

        /* Filter */
        .swagger-ui .filter-container {
          background: rgba(26, 26, 46, 0.8) !important;
          border: 1px solid var(--border-color) !important;
          border-radius: 8px !important;
          padding: 15px !important;
          margin: 20px 0 !important;
          backdrop-filter: blur(10px) !important;
        }

        .swagger-ui .filter input {
          background: rgba(15, 15, 35, 0.8) !important;
          border: 1px solid var(--border-color) !important;
          border-radius: 6px !important;
          color: var(--text-primary) !important;
          padding: 10px !important;
          width: 100% !important;
        }

        /* Server Selection Dropdown - Fix white background */
        .swagger-ui .scheme-container {
          background: rgba(26, 26, 46, 0.8) !important;
          border: 1px solid var(--border-color) !important;
          border-radius: 8px !important;
          padding: 20px !important;
          margin: 20px 0 !important;
          backdrop-filter: blur(10px) !important;
        }

        .swagger-ui .scheme-container .schemes {
          background: transparent !important;
        }

        .swagger-ui .scheme-container .schemes > label {
          color: var(--text-primary) !important;
          font-weight: 600 !important;
          margin-bottom: 10px !important;
          display: block !important;
        }

        .swagger-ui .scheme-container select {
          background: rgba(15, 15, 35, 0.9) !important;
          border: 1px solid var(--border-color) !important;
          border-radius: 6px !important;
          color: var(--text-primary) !important;
          padding: 10px 15px !important;
          font-size: 14px !important;
          font-weight: 500 !important;
          min-width: 200px !important;
          transition: all 0.3s ease !important;
        }

        .swagger-ui .scheme-container select:focus {
          border-color: var(--accent-primary) !important;
          box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2) !important;
          outline: none !important;
        }

        .swagger-ui .scheme-container select option {
          background: var(--bg-secondary) !important;
          color: var(--text-primary) !important;
          padding: 10px !important;
        }

        /* Global background override for any remaining white elements */
        .swagger-ui *,
        .swagger-ui *:before,
        .swagger-ui *:after {
          background-color: transparent !important;
        }

        .swagger-ui .wrapper,
        .swagger-ui .swagger-ui,
        .swagger-ui .swagger-container {
          background: transparent !important;
        }

        /* Force dark background on specific problematic elements */
        .swagger-ui .servers > label > select,
        .swagger-ui .servers select,
        .swagger-ui select,
        .swagger-ui input,
        .swagger-ui textarea {
          background: rgba(15, 15, 35, 0.9) !important;
          color: var(--text-primary) !important;
          border: 1px solid var(--border-color) !important;
        }

        /* Additional server-related styling */
        .swagger-ui .servers {
          background: rgba(26, 26, 46, 0.8) !important;
          border: 1px solid var(--border-color) !important;
          border-radius: 8px !important;
          padding: 20px !important;
          margin: 20px 0 !important;
          backdrop-filter: blur(10px) !important;
        }

        .swagger-ui .servers > label {
          color: var(--text-primary) !important;
          font-weight: 600 !important;
          margin-bottom: 10px !important;
          display: block !important;
        }

        .swagger-ui .servers > label > span {
          color: var(--text-primary) !important;
        }

        /* Beautiful animations and effects */
        .swagger-ui .opblock.is-open {
          box-shadow: 0 0 20px rgba(59, 130, 246, 0.2) !important;
          border-color: var(--accent-primary) !important;
        }

        .swagger-ui .opblock:hover .opblock-summary-method {
          transform: scale(1.05) !important;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
        }

        .swagger-ui input:focus,
        .swagger-ui textarea:focus,
        .swagger-ui select:focus {
          transform: translateY(-1px) !important;
          box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2), 0 0 0 2px rgba(59, 130, 246, 0.2) !important;
        }

        /* Beautiful scrollbar for the entire page */
        body::-webkit-scrollbar {
          width: 12px;
        }

        body::-webkit-scrollbar-track {
          background: var(--bg-primary);
        }

        body::-webkit-scrollbar-thumb {
          background: linear-gradient(45deg, var(--accent-primary), var(--accent-secondary));
          border-radius: 6px;
          border: 2px solid var(--bg-primary);
        }

        body::-webkit-scrollbar-thumb:hover {
          background: linear-gradient(45deg, #2563eb, #059669);
        }
      \`;
      document.head.appendChild(style);
    }

    window.onload = function() {
      // Apply dark theme first
      applyDarkTheme();

      const ui = SwaggerUIBundle({
        url: '/api/docs/swagger.json',
        dom_id: '#swagger-ui',
        deepLinking: true,
        presets: [
          SwaggerUIBundle.presets.apis,
          SwaggerUIStandalonePreset
        ],
        plugins: [
          SwaggerUIBundle.plugins.DownloadUrl
        ],
        layout: "StandaloneLayout",
        persistAuthorization: true,
        displayRequestDuration: true,
        docExpansion: 'list',
        filter: true,
        showExtensions: true,
        showCommonExtensions: true,
        tryItOutEnabled: true,
        supportedSubmitMethods: ['get', 'post', 'put', 'delete', 'patch'],
        onComplete: function() {
          console.log('Swagger UI loaded successfully');
          // Hide loading screen after Swagger UI is ready
          setTimeout(hideLoading, 1000);

          // Add stagger animation to operation blocks
          const operations = document.querySelectorAll('.swagger-ui .opblock');
          operations.forEach((op, index) => {
            op.style.animationDelay = \`\${index * 0.1}s\`;
          });
        },
        onFailure: function(error) {
          console.error('Failed to load Swagger UI:', error);
          hideLoading();
        }
      });
    };
  </script>
</body>
</html>`;

    return new NextResponse(swaggerUiHtml, {
      headers: {
        "Content-Type": "text/html",
        "Cache-Control": "no-cache, no-store, must-revalidate",
        Pragma: "no-cache",
        Expires: "0",
      },
    });
  } catch (error) {
    console.error("Error generating Swagger UI:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Failed to generate API documentation",
        error: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
