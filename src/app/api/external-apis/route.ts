import { NextRequest, NextResponse } from "next/server";
import { API_CONFIG } from "@/lib/api-config";

/**
 * @swagger
 * components:
 *   schemas:
 *     ExternalAPIEndpoint:
 *       type: object
 *       properties:
 *         method:
 *           type: string
 *           enum: [GET, POST, PUT, DELETE, PATCH]
 *           example: GET
 *         endpoint:
 *           type: string
 *           example: /api/v1/users
 *         fullUrl:
 *           type: string
 *           example: http://localhost:8080/users/api/v1/users
 *         description:
 *           type: string
 *           example: Get all users from the system
 *         parameters:
 *           type: array
 *           items:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               type:
 *                 type: string
 *               required:
 *                 type: boolean
 *               description:
 *                 type: string
 *         requestBody:
 *           type: object
 *           description: Expected request body schema
 *         responseBody:
 *           type: object
 *           description: Expected response body schema
 *     ExternalAPIService:
 *       type: object
 *       properties:
 *         serviceName:
 *           type: string
 *           example: AUTH_SERVICE
 *         baseUrl:
 *           type: string
 *           example: http://localhost:8080/auth
 *         description:
 *           type: string
 *           example: Spring Boot Authentication Service
 *         swaggerUrl:
 *           type: string
 *           example: http://localhost:8080/auth/swagger-ui.html
 *         endpoints:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/ExternalAPIEndpoint'
 */

/**
 * @swagger
 * /api/external-apis:
 *   get:
 *     summary: Get all external Spring Boot APIs
 *     description: |
 *       Returns comprehensive documentation of all Spring Boot microservices 
 *       that this Next.js API Gateway integrates with.
 *       
 *       This endpoint provides:
 *       - Service base URLs and descriptions
 *       - Complete endpoint listings with methods
 *       - Parameter and request/response schemas
 *       - Links to individual service Swagger documentation
 *       
 *       **Use this endpoint to:**
 *       - Understand the complete microservices architecture
 *       - Get direct links to Spring Boot service documentation
 *       - See the mapping between Next.js gateway endpoints and Spring Boot services
 *     tags: [External API Integration]
 *     parameters:
 *       - in: query
 *         name: service
 *         schema:
 *           type: string
 *           enum: [AUTH_SERVICE, USER_SERVICE, CONTENT_SERVICE, FINANCE_SERVICE, ANALYTICS_SERVICE, NOTIFICATION_SERVICE, FILE_SERVICE, RESULTS_SERVICE, SPONSOR_SERVICE]
 *         description: Filter by specific service
 *       - in: query
 *         name: includeEndpoints
 *         schema:
 *           type: boolean
 *           default: true
 *         description: Include detailed endpoint information
 *     responses:
 *       200:
 *         description: External APIs retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: External APIs retrieved successfully
 *                 data:
 *                   type: object
 *                   properties:
 *                     architecture:
 *                       type: object
 *                       properties:
 *                         pattern:
 *                           type: string
 *                           example: API Gateway Pattern
 *                         description:
 *                           type: string
 *                         totalServices:
 *                           type: integer
 *                           example: 9
 *                     services:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/ExternalAPIService'
 *       400:
 *         description: Invalid service parameter
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const serviceFilter = searchParams.get("service");
    const includeEndpoints = searchParams.get("includeEndpoints") !== "false";

    // Validate service filter
    if (serviceFilter && !API_CONFIG.EXTERNAL_APIS[serviceFilter as keyof typeof API_CONFIG.EXTERNAL_APIS]) {
      return NextResponse.json(
        {
          success: false,
          message: `Invalid service parameter. Valid services: ${Object.keys(API_CONFIG.EXTERNAL_APIS).join(", ")}`,
          statusCode: 400,
        },
        { status: 400 }
      );
    }

    // Build service documentation
    const services = Object.entries(API_CONFIG.EXTERNAL_APIS)
      .filter(([key]) => !serviceFilter || key === serviceFilter)
      .map(([serviceName, config]) => {
        const service: any = {
          serviceName,
          baseUrl: config.baseUrl,
          description: getServiceDescription(serviceName),
          swaggerUrl: `${config.baseUrl}/swagger-ui.html`,
          healthCheckUrl: `${config.baseUrl}/actuator/health`,
        };

        if (includeEndpoints) {
          service.endpoints = Object.entries(config.endpoints).map(([endpointName, endpoint]) => ({
            name: endpointName,
            method: getEndpointMethod(endpointName),
            endpoint,
            fullUrl: `${config.baseUrl}${endpoint}`,
            description: getEndpointDescription(serviceName, endpointName),
            gatewayMapping: getGatewayMapping(serviceName, endpointName),
          }));
        }

        return service;
      });

    const responseData = {
      architecture: {
        pattern: "API Gateway Pattern",
        description: "Next.js acts as an API Gateway, routing requests to appropriate Spring Boot microservices",
        totalServices: Object.keys(API_CONFIG.EXTERNAL_APIS).length,
        benefits: [
          "Unified API interface for frontend clients",
          "Authentication and authorization at gateway level",
          "Request/response transformation",
          "Error handling and logging",
          "Rate limiting and caching",
        ],
      },
      services,
      integrationFlow: {
        steps: [
          "1. Client sends request to Next.js API Gateway",
          "2. Gateway validates authentication and request data",
          "3. Gateway forwards request to appropriate Spring Boot service",
          "4. Spring Boot service processes request and returns response",
          "5. Gateway transforms response if needed and returns to client",
        ],
      },
    };

    return NextResponse.json({
      success: true,
      message: "External APIs retrieved successfully",
      data: responseData,
      meta: {
        totalServices: services.length,
        filteredBy: serviceFilter || "all",
        includeEndpoints,
        generatedAt: new Date().toISOString(),
      },
    });
  } catch (error) {
    console.error("Get external APIs error:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Internal server error",
        error: error instanceof Error ? error.message : "Unknown error",
        statusCode: 500,
      },
      { status: 500 }
    );
  }
}

// Helper functions
function getServiceDescription(serviceName: string): string {
  const descriptions: Record<string, string> = {
    AUTH_SERVICE: "Handles user authentication, authorization, and JWT token management",
    USER_SERVICE: "Manages user accounts, profiles, and user-related operations",
    CONTENT_SERVICE: "Manages learning content including tracks, subjects, and chapters",
    FINANCE_SERVICE: "Handles financial transactions, payments, and revenue tracking",
    ANALYTICS_SERVICE: "Provides analytics, reporting, and dashboard statistics",
    NOTIFICATION_SERVICE: "Manages email, SMS, and push notifications",
    FILE_SERVICE: "Handles file uploads, storage, and retrieval operations",
    RESULTS_SERVICE: "Manages test results, scoring, and performance analytics",
    SPONSOR_SERVICE: "Manages sponsors, advertisements, and promotional content",
  };
  return descriptions[serviceName] || "Spring Boot microservice";
}

function getEndpointMethod(endpointName: string): string {
  if (endpointName.startsWith("get") || endpointName.includes("List")) return "GET";
  if (endpointName.startsWith("create") || endpointName.startsWith("add")) return "POST";
  if (endpointName.startsWith("update") || endpointName.startsWith("edit")) return "PUT";
  if (endpointName.startsWith("delete") || endpointName.startsWith("remove")) return "DELETE";
  if (endpointName.includes("process") || endpointName.includes("send")) return "POST";
  return "GET";
}

function getEndpointDescription(serviceName: string, endpointName: string): string {
  // This would typically come from a configuration file or database
  return `${endpointName} operation in ${serviceName}`;
}

function getGatewayMapping(serviceName: string, endpointName: string): string {
  // Map to corresponding Next.js gateway endpoints
  const mappings: Record<string, Record<string, string>> = {
    AUTH_SERVICE: {
      login: "/api/auth",
      logout: "/api/auth/logout",
      refresh: "/api/auth/refresh",
    },
    USER_SERVICE: {
      getUsers: "/api/users/subscribers",
      createUser: "/api/users/subscribers",
      getUserById: "/api/users/subscribers/{id}",
    },
    CONTENT_SERVICE: {
      getTracks: "/api/content/tracks",
      createTrack: "/api/content/tracks",
    },
    ANALYTICS_SERVICE: {
      getDashboardStats: "/api/dashboard/stats",
    },
  };
  
  return mappings[serviceName]?.[endpointName] || "Not mapped to gateway endpoint";
}
