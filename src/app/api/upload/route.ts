import { NextRequest, NextResponse } from 'next/server';

/**
 * @swagger
 * components:
 *   schemas:
 *     FileUploadResponse:
 *       type: object
 *       properties:
 *         success:
 *           type: boolean
 *           example: true
 *         message:
 *           type: string
 *           example: File uploaded successfully
 *         data:
 *           type: object
 *           properties:
 *             filename:
 *               type: string
 *               example: "1642345678_document.pdf"
 *             originalName:
 *               type: string
 *               example: "document.pdf"
 *             size:
 *               type: integer
 *               example: 1024000
 *               description: File size in bytes
 *             mimeType:
 *               type: string
 *               example: "application/pdf"
 *             url:
 *               type: string
 *               example: "/uploads/1642345678_document.pdf"
 *             uploadedAt:
 *               type: string
 *               format: date-time
 *               example: "2024-01-15T10:30:00Z"
 */

/**
 * @swagger
 * /api/upload:
 *   post:
 *     summary: Upload file
 *     description: Upload a file to the server (images, documents, etc.)
 *     tags: [Content]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               file:
 *                 type: string
 *                 format: binary
 *                 description: File to upload
 *               type:
 *                 type: string
 *                 enum: [image, document, video, audio]
 *                 description: Type of file being uploaded
 *                 example: image
 *               category:
 *                 type: string
 *                 enum: [avatar, content, track, subject, chapter, sponsor, ad]
 *                 description: Category/purpose of the file
 *                 example: content
 *             required:
 *               - file
 *     responses:
 *       200:
 *         description: File uploaded successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/FileUploadResponse'
 *       400:
 *         description: Invalid file or request
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       413:
 *         description: File too large
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       415:
 *         description: Unsupported file type
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const type = formData.get('type') as string;
    const category = formData.get('category') as string;

    if (!file) {
      return NextResponse.json(
        {
          success: false,
          message: 'No file provided',
          statusCode: 400,
        },
        { status: 400 }
      );
    }

    // Validate file size (10MB limit)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      return NextResponse.json(
        {
          success: false,
          message: 'File size exceeds 10MB limit',
          statusCode: 413,
        },
        { status: 413 }
      );
    }

    // Validate file type
    const allowedTypes = {
      image: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
      document: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
      video: ['video/mp4', 'video/avi', 'video/mov', 'video/wmv'],
      audio: ['audio/mp3', 'audio/wav', 'audio/ogg'],
    };

    const fileType = type as keyof typeof allowedTypes;
    if (fileType && allowedTypes[fileType] && !allowedTypes[fileType].includes(file.type)) {
      return NextResponse.json(
        {
          success: false,
          message: `Invalid file type for ${type}. Allowed types: ${allowedTypes[fileType].join(', ')}`,
          statusCode: 415,
        },
        { status: 415 }
      );
    }

    // Generate unique filename
    const timestamp = Date.now();
    const extension = file.name.split('.').pop();
    const filename = `${timestamp}_${file.name.replace(/[^a-zA-Z0-9.-]/g, '_')}`;

    // In a real implementation, you would:
    // 1. Save the file to a storage service (AWS S3, Google Cloud Storage, etc.)
    // 2. Store file metadata in database
    // 3. Generate proper URLs
    // 4. Handle different file categories appropriately

    // Mock response
    const mockResponse = {
      filename,
      originalName: file.name,
      size: file.size,
      mimeType: file.type,
      url: `/uploads/${filename}`,
      uploadedAt: new Date().toISOString(),
    };

    return NextResponse.json({
      success: true,
      message: 'File uploaded successfully',
      data: mockResponse,
    });
  } catch (error) {
    console.error('File upload error:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Internal server error',
        error: error instanceof Error ? error.message : 'Unknown error',
        statusCode: 500,
      },
      { status: 500 }
    );
  }
}

/**
 * @swagger
 * /api/upload:
 *   get:
 *     summary: Get upload guidelines
 *     description: Get information about file upload limits and supported formats
 *     tags: [Content]
 *     responses:
 *       200:
 *         description: Upload guidelines retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Upload guidelines retrieved successfully
 *                 data:
 *                   type: object
 *                   properties:
 *                     maxFileSize:
 *                       type: string
 *                       example: "10MB"
 *                     supportedTypes:
 *                       type: object
 *                       properties:
 *                         image:
 *                           type: array
 *                           items:
 *                             type: string
 *                           example: ["jpeg", "png", "gif", "webp"]
 *                         document:
 *                           type: array
 *                           items:
 *                             type: string
 *                           example: ["pdf", "doc", "docx"]
 *                         video:
 *                           type: array
 *                           items:
 *                             type: string
 *                           example: ["mp4", "avi", "mov", "wmv"]
 *                         audio:
 *                           type: array
 *                           items:
 *                             type: string
 *                           example: ["mp3", "wav", "ogg"]
 *                     categories:
 *                       type: array
 *                       items:
 *                         type: string
 *                       example: ["avatar", "content", "track", "subject", "chapter", "sponsor", "ad"]
 */
export async function GET() {
  try {
    const guidelines = {
      maxFileSize: '10MB',
      supportedTypes: {
        image: ['jpeg', 'png', 'gif', 'webp'],
        document: ['pdf', 'doc', 'docx'],
        video: ['mp4', 'avi', 'mov', 'wmv'],
        audio: ['mp3', 'wav', 'ogg'],
      },
      categories: ['avatar', 'content', 'track', 'subject', 'chapter', 'sponsor', 'ad'],
    };

    return NextResponse.json({
      success: true,
      message: 'Upload guidelines retrieved successfully',
      data: guidelines,
    });
  } catch (error) {
    console.error('Get upload guidelines error:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Internal server error',
        error: error instanceof Error ? error.message : 'Unknown error',
        statusCode: 500,
      },
      { status: 500 }
    );
  }
}
