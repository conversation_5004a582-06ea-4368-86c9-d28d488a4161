import { NextRequest, NextResponse } from "next/server";
import { subscriber } from "@/models/subscribers";
import { dummyMembers } from "@/lib/dummy-data";

/**
 * @swagger
 * /api/users/subscribers/{id}:
 *   get:
 *     summary: Get subscriber by ID
 *     description: Retrieve a specific subscriber by their ID
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Subscriber ID
 *     responses:
 *       200:
 *         description: Subscriber retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/Success'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/Subscriber'
 *       404:
 *         description: Subscriber not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;

    const subscriber = dummyMembers.find((member) => member.id === id);

    if (!subscriber) {
      return NextResponse.json(
        {
          success: false,
          message: "Subscriber not found",
          statusCode: 404,
        },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: "Subscriber retrieved successfully",
      data: subscriber,
    });
  } catch (error) {
    console.error("Get subscriber error:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Internal server error",
        error: error instanceof Error ? error.message : "Unknown error",
        statusCode: 500,
      },
      { status: 500 }
    );
  }
}

/**
 * @swagger
 * /api/users/subscribers/{id}:
 *   put:
 *     summary: Update subscriber
 *     description: Update a specific subscriber's information
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Subscriber ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 example: John Doe Updated
 *               email:
 *                 type: string
 *                 format: email
 *                 example: <EMAIL>
 *               status:
 *                 type: string
 *                 enum: [active, inactive, pending]
 *                 example: active
 *               role:
 *                 type: string
 *                 enum: [admin, editor, viewer, content]
 *                 example: editor
 *               phone:
 *                 type: string
 *                 example: "+91 9876543210"
 *               avatar:
 *                 type: string
 *                 example: "/new-avatar.png"
 *     responses:
 *       200:
 *         description: Subscriber updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/Success'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/Subscriber'
 *       400:
 *         description: Invalid request data
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       404:
 *         description: Subscriber not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       409:
 *         description: Email already exists
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const body = await request.json();

    const subscriberIndex = dummyMembers.findIndex(
      (member) => member.id === id
    );

    if (subscriberIndex === -1) {
      return NextResponse.json(
        {
          success: false,
          message: "Subscriber not found",
          statusCode: 404,
        },
        { status: 404 }
      );
    }

    // Validate email format if provided
    if (body.email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(body.email)) {
        return NextResponse.json(
          {
            success: false,
            message: "Invalid email format",
            statusCode: 400,
          },
          { status: 400 }
        );
      }

      // Check if email already exists (excluding current user)
      const existingUser = dummyMembers.find(
        (member) => member.email === body.email && member.id !== id
      );
      if (existingUser) {
        return NextResponse.json(
          {
            success: false,
            message: "Email already exists",
            statusCode: 409,
          },
          { status: 409 }
        );
      }
    }

    // Update subscriber (in real implementation, update in database)
    const currentSubscriber = dummyMembers[subscriberIndex];
    const updatedSubscriber: subscriber = {
      ...currentSubscriber,
      name: body.name || currentSubscriber.name,
      email: body.email || currentSubscriber.email,
      status: body.status || currentSubscriber.status,
      role: body.role || currentSubscriber.role,
      phone: body.phone !== undefined ? body.phone : currentSubscriber.phone,
      avatar:
        body.avatar !== undefined ? body.avatar : currentSubscriber.avatar,
    };

    return NextResponse.json({
      success: true,
      message: "Subscriber updated successfully",
      data: updatedSubscriber,
    });
  } catch (error) {
    console.error("Update subscriber error:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Internal server error",
        error: error instanceof Error ? error.message : "Unknown error",
        statusCode: 500,
      },
      { status: 500 }
    );
  }
}

/**
 * @swagger
 * /api/users/subscribers/{id}:
 *   delete:
 *     summary: Delete subscriber
 *     description: Delete a specific subscriber by their ID
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Subscriber ID
 *     responses:
 *       200:
 *         description: Subscriber deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Subscriber deleted successfully
 *       404:
 *         description: Subscriber not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;

    const subscriberIndex = dummyMembers.findIndex(
      (member) => member.id === id
    );

    if (subscriberIndex === -1) {
      return NextResponse.json(
        {
          success: false,
          message: "Subscriber not found",
          statusCode: 404,
        },
        { status: 404 }
      );
    }

    // In a real implementation, you would delete from database
    // For now, we'll just simulate the deletion

    return NextResponse.json({
      success: true,
      message: "Subscriber deleted successfully",
    });
  } catch (error) {
    console.error("Delete subscriber error:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Internal server error",
        error: error instanceof Error ? error.message : "Unknown error",
        statusCode: 500,
      },
      { status: 500 }
    );
  }
}
