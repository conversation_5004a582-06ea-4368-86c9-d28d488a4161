import { NextRequest, NextResponse } from "next/server";
import { subscriber } from "@/models/subscribers";
import { dummyMembers } from "@/lib/dummy-data";

/**
 * @swagger
 * components:
 *   schemas:
 *     Subscriber:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           example: "1"
 *         name:
 *           type: string
 *           example: <PERSON>
 *         email:
 *           type: string
 *           format: email
 *           example: <EMAIL>
 *         status:
 *           type: string
 *           enum: [active, inactive, pending]
 *           example: active
 *         role:
 *           type: string
 *           enum: [admin, editor, viewer, content]
 *           example: admin
 *         createdContent:
 *           type: number
 *           example: 12
 *           description: Number of content items created (optional)
 *         joinDate:
 *           type: string
 *           format: date
 *           example: "2024-08-01"
 *         lastLogin:
 *           type: string
 *           format: date
 *           example: "2025-09-15"
 *         phone:
 *           type: string
 *           example: "+91 6282745361"
 *           description: Phone number (optional)
 *         avatar:
 *           type: string
 *           example: "/professional-headshot.png"
 *           description: Avatar image URL (optional)
 *     CreateSubscriberRequest:
 *       type: object
 *       required:
 *         - name
 *         - email
 *         - status
 *         - role
 *       properties:
 *         name:
 *           type: string
 *           example: <PERSON>
 *         email:
 *           type: string
 *           format: email
 *           example: <EMAIL>
 *         status:
 *           type: string
 *           enum: [active, inactive, pending]
 *           example: active
 *         role:
 *           type: string
 *           enum: [admin, editor, viewer, content]
 *           example: viewer
 *         phone:
 *           type: string
 *           example: "+91 1234567890"
 *         avatar:
 *           type: string
 *           example: "/avatar.png"
 */

/**
 * @swagger
 * /api/users/subscribers:
 *   get:
 *     summary: Get all subscribers (Gateway)
 *     description: |
 *       Retrieve a paginated list of all subscribers. This endpoint acts as a gateway
 *       that forwards requests to the Spring Boot User Service.
 *
 *       **Integration Details:**
 *       - **Spring Boot Endpoint**: `GET /api/v1/users`
 *       - **Service URL**: `http://localhost:8080/users/api/v1/users`
 *       - **Flow**: Next.js → Spring Boot User Service → Response
 *
 *       **What this endpoint does:**
 *       1. Validates query parameters
 *       2. Forwards request to Spring Boot User Service
 *       3. Transforms response format if needed
 *       4. Returns paginated user data
 *     tags: [Users]
 *     externalDocs:
 *       description: Spring Boot User Service Documentation
 *       url: http://localhost:8080/users/swagger-ui.html
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 10
 *         description: Number of items per page
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [active, inactive, pending]
 *         description: Filter by status
 *       - in: query
 *         name: role
 *         schema:
 *           type: string
 *           enum: [admin, editor, viewer, content]
 *         description: Filter by role
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search by name or email
 *     responses:
 *       200:
 *         description: Subscribers retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/PaginatedResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/Subscriber'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const status = searchParams.get("status");
    const role = searchParams.get("role");
    const search = searchParams.get("search");

    // Validate pagination parameters
    if (page < 1 || limit < 1 || limit > 100) {
      return NextResponse.json(
        {
          success: false,
          message: "Invalid pagination parameters",
          statusCode: 400,
        },
        { status: 400 }
      );
    }

    // SPRING BOOT INTEGRATION:
    // In production, this would call the Spring Boot User Service:
    // const queryParams = new URLSearchParams({
    //   page: page.toString(),
    //   limit: limit.toString(),
    //   ...(status && { status }),
    //   ...(role && { role }),
    //   ...(search && { search })
    // });
    // const response = await fetch(`${API_CONFIG.EXTERNAL_APIS.USER_SERVICE.baseUrl}/api/v1/users?${queryParams}`);
    // const userData = await response.json();

    // MOCK IMPLEMENTATION (Replace with Spring Boot call):
    let filteredData = [...dummyMembers];

    // Apply filters
    if (status) {
      filteredData = filteredData.filter((member) => member.status === status);
    }
    if (role) {
      filteredData = filteredData.filter((member) => member.role === role);
    }
    if (search) {
      const searchLower = search.toLowerCase();
      filteredData = filteredData.filter(
        (member) =>
          member.name.toLowerCase().includes(searchLower) ||
          member.email.toLowerCase().includes(searchLower)
      );
    }

    // Calculate pagination
    const total = filteredData.length;
    const totalPages = Math.ceil(total / limit);
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedData = filteredData.slice(startIndex, endIndex);

    return NextResponse.json({
      success: true,
      message: "Subscribers retrieved successfully",
      data: paginatedData,
      meta: {
        page,
        limit,
        total,
        totalPages,
      },
    });
  } catch (error) {
    console.error("Get subscribers error:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Internal server error",
        error: error instanceof Error ? error.message : "Unknown error",
        statusCode: 500,
      },
      { status: 500 }
    );
  }
}

/**
 * @swagger
 * /api/users/subscribers:
 *   post:
 *     summary: Create a new subscriber
 *     description: Create a new subscriber with the provided information
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateSubscriberRequest'
 *     responses:
 *       201:
 *         description: Subscriber created successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/Success'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/Subscriber'
 *       400:
 *         description: Invalid request data
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       409:
 *         description: Email already exists
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate required fields
    const requiredFields = ["name", "email", "status", "role"];
    const missingFields = requiredFields.filter((field) => !body[field]);

    if (missingFields.length > 0) {
      return NextResponse.json(
        {
          success: false,
          message: `Missing required fields: ${missingFields.join(", ")}`,
          statusCode: 400,
        },
        { status: 400 }
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(body.email)) {
      return NextResponse.json(
        {
          success: false,
          message: "Invalid email format",
          statusCode: 400,
        },
        { status: 400 }
      );
    }

    // Check if email already exists
    const existingUser = dummyMembers.find(
      (member) => member.email === body.email
    );
    if (existingUser) {
      return NextResponse.json(
        {
          success: false,
          message: "Email already exists",
          statusCode: 409,
        },
        { status: 409 }
      );
    }

    // Create new subscriber
    const newSubscriber: subscriber = {
      id: (dummyMembers.length + 1).toString(),
      name: body.name,
      email: body.email,
      status: body.status,
      role: body.role,
      joinDate: new Date().toISOString().split("T")[0],
      lastLogin: new Date().toISOString().split("T")[0],
      phone: body.phone || undefined,
      avatar: body.avatar || undefined,
      createdContent: body.role === "content" ? 0 : undefined,
    };

    // In a real implementation, you would save to database
    // For now, we'll just return the created subscriber

    return NextResponse.json(
      {
        success: true,
        message: "Subscriber created successfully",
        data: newSubscriber,
      },
      { status: 201 }
    );
  } catch (error) {
    console.error("Create subscriber error:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Internal server error",
        error: error instanceof Error ? error.message : "Unknown error",
        statusCode: 500,
      },
      { status: 500 }
    );
  }
}
