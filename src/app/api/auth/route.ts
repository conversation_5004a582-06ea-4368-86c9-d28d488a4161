import { NextRequest, NextResponse } from "next/server";
import { Auth } from "@/models/auth";

/**
 * @swagger
 * components:
 *   schemas:
 *     LoginRequest:
 *       type: object
 *       required:
 *         - email
 *         - password
 *       properties:
 *         email:
 *           type: string
 *           format: email
 *           example: <EMAIL>
 *           description: User's email address
 *         password:
 *           type: string
 *           format: password
 *           example: password123
 *           description: User's password
 *     LoginResponse:
 *       type: object
 *       properties:
 *         success:
 *           type: boolean
 *           example: true
 *         message:
 *           type: string
 *           example: Login successful
 *         data:
 *           type: object
 *           properties:
 *             user:
 *               type: object
 *               properties:
 *                 id:
 *                   type: string
 *                   example: "1"
 *                 email:
 *                   type: string
 *                   example: <EMAIL>
 *                 name:
 *                   type: string
 *                   example: Admin User
 *                 role:
 *                   type: string
 *                   example: admin
 *             token:
 *               type: string
 *               example: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
 *             refreshToken:
 *               type: string
 *               example: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
 *             expiresIn:
 *               type: number
 *               example: 3600
 *               description: Token expiration time in seconds
 */

/**
 * @swagger
 * /api/auth:
 *   post:
 *     summary: User login
 *     description: Authenticate user with email and password
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/LoginRequest'
 *     responses:
 *       200:
 *         description: Login successful
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/LoginResponse'
 *       400:
 *         description: Invalid request data
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Invalid credentials
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
export async function POST(request: NextRequest) {
  try {
    const body: Auth = await request.json();

    // Validate required fields
    if (!body.email || !body.password) {
      return NextResponse.json(
        {
          success: false,
          message: "Email and password are required",
          statusCode: 400,
        },
        { status: 400 }
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(body.email)) {
      return NextResponse.json(
        {
          success: false,
          message: "Invalid email format",
          statusCode: 400,
        },
        { status: 400 }
      );
    }

    // TODO: Replace with actual authentication logic
    // This is a mock implementation
    const mockUsers = [
      {
        id: "1",
        email: "<EMAIL>",
        password: "password123",
        name: "Admin User",
        role: "admin",
      },
      {
        id: "2",
        email: "<EMAIL>",
        password: "password123",
        name: "Regular User",
        role: "user",
      },
    ];

    const user = mockUsers.find(
      (u) => u.email === body.email && u.password === body.password
    );

    if (!user) {
      return NextResponse.json(
        {
          success: false,
          message: "Invalid email or password",
          statusCode: 401,
        },
        { status: 401 }
      );
    }

    // Generate mock JWT token (replace with actual JWT implementation)
    const mockToken = `mock-jwt-token-${user.id}-${Date.now()}`;
    const mockRefreshToken = `mock-refresh-token-${user.id}-${Date.now()}`;

    return NextResponse.json({
      success: true,
      message: "Login successful",
      data: {
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          role: user.role,
        },
        token: mockToken,
        refreshToken: mockRefreshToken,
        expiresIn: 3600, // 1 hour
      },
    });
  } catch (error) {
    console.error("Login error:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Internal server error",
        error: error instanceof Error ? error.message : "Unknown error",
        statusCode: 500,
      },
      { status: 500 }
    );
  }
}
