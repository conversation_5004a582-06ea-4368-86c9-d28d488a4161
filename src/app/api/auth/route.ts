import { NextRequest, NextResponse } from "next/server";
import { Auth } from "@/models/auth";

/**
 * @swagger
 * components:
 *   schemas:
 *     LoginRequest:
 *       type: object
 *       required:
 *         - email
 *         - password
 *       properties:
 *         email:
 *           type: string
 *           format: email
 *           example: <EMAIL>
 *           description: User's email address
 *         password:
 *           type: string
 *           format: password
 *           example: password123
 *           description: User's password
 *     LoginResponse:
 *       type: object
 *       properties:
 *         success:
 *           type: boolean
 *           example: true
 *         message:
 *           type: string
 *           example: Login successful
 *         data:
 *           type: object
 *           properties:
 *             user:
 *               type: object
 *               properties:
 *                 id:
 *                   type: string
 *                   example: "1"
 *                 email:
 *                   type: string
 *                   example: <EMAIL>
 *                 name:
 *                   type: string
 *                   example: Admin User
 *                 role:
 *                   type: string
 *                   example: admin
 *             token:
 *               type: string
 *               example: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
 *             refreshToken:
 *               type: string
 *               example: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
 *             expiresIn:
 *               type: number
 *               example: 3600
 *               description: Token expiration time in seconds
 */

/**
 * @swagger
 * /api/auth:
 *   post:
 *     summary: User login (Gateway)
 *     description: |
 *       Authenticate user with email and password. This endpoint acts as a gateway that forwards
 *       authentication requests to the Spring Boot Auth Service.
 *
 *       **Integration Details:**
 *       - **Spring Boot Endpoint**: `POST /api/v1/auth/login`
 *       - **Service URL**: `http://localhost:8080/auth/api/v1/auth/login`
 *       - **Flow**: Next.js → Spring Boot Auth Service → Response
 *
 *       **What this endpoint does:**
 *       1. Validates input data
 *       2. Forwards credentials to Spring Boot Auth Service
 *       3. Receives JWT token from Spring Boot
 *       4. Returns formatted response to client
 *     tags: [Authentication]
 *     externalDocs:
 *       description: Spring Boot Auth Service Documentation
 *       url: http://localhost:8080/auth/swagger-ui.html
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/LoginRequest'
 *     responses:
 *       200:
 *         description: Login successful
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/LoginResponse'
 *       400:
 *         description: Invalid request data
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Invalid credentials
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
export async function POST(request: NextRequest) {
  try {
    const body: Auth = await request.json();

    // Validate required fields
    if (!body.email || !body.password) {
      return NextResponse.json(
        {
          success: false,
          message: "Email and password are required",
          statusCode: 400,
        },
        { status: 400 }
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(body.email)) {
      return NextResponse.json(
        {
          success: false,
          message: "Invalid email format",
          statusCode: 400,
        },
        { status: 400 }
      );
    }

    // SPRING BOOT INTEGRATION:
    // In production, this would call the Spring Boot Auth Service:
    // const response = await fetch(`${API_CONFIG.EXTERNAL_APIS.AUTH_SERVICE.baseUrl}/api/v1/auth/login`, {
    //   method: 'POST',
    //   headers: { 'Content-Type': 'application/json' },
    //   body: JSON.stringify({ email: body.email, password: body.password })
    // });
    // const authResult = await response.json();

    // MOCK IMPLEMENTATION (Replace with Spring Boot call):
    const mockUsers = [
      {
        id: "1",
        email: "<EMAIL>",
        password: "password123",
        name: "Admin User",
        role: "admin",
      },
      {
        id: "2",
        email: "<EMAIL>",
        password: "password123",
        name: "Regular User",
        role: "user",
      },
    ];

    const user = mockUsers.find(
      (u) => u.email === body.email && u.password === body.password
    );

    if (!user) {
      return NextResponse.json(
        {
          success: false,
          message: "Invalid email or password",
          statusCode: 401,
        },
        { status: 401 }
      );
    }

    // Generate mock JWT token (replace with actual JWT implementation)
    const mockToken = `mock-jwt-token-${user.id}-${Date.now()}`;
    const mockRefreshToken = `mock-refresh-token-${user.id}-${Date.now()}`;

    return NextResponse.json({
      success: true,
      message: "Login successful",
      data: {
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          role: user.role,
        },
        token: mockToken,
        refreshToken: mockRefreshToken,
        expiresIn: 3600, // 1 hour
      },
    });
  } catch (error) {
    console.error("Login error:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Internal server error",
        error: error instanceof Error ? error.message : "Unknown error",
        statusCode: 500,
      },
      { status: 500 }
    );
  }
}
