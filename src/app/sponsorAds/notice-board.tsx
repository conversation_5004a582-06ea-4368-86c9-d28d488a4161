"use client";

import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import React, { useState } from "react";
import { ImageIcon, Edit, Trash2 } from "lucide-react";

interface NoticePosition {
  id: number;
  position: string;
  text: string;
  mobileImage?: File | null;
  webImage?: File | null;
  mobileImagePreview?: string;
  webImagePreview?: string;
}

export default function NoticeBoard() {
  const [position, setPosition] = useState("");
  const [text, setText] = useState("");
  const [mobileImage, setMobileImage] = useState<File | null>(null);
  const [webImage, setWebImage] = useState<File | null>(null);
  const [mobileImagePreview, setMobileImagePreview] = useState<string>("");
  const [webImagePreview, setWebImagePreview] = useState<string>("");
  const [positions, setPositions] = useState<NoticePosition[]>([
    {
      id: 1,
      position: "Position #1",
      text: "Text",
      mobileImage: null,
      webImage: null,
    },
    {
      id: 2,
      position: "Position #2",
      text: "Text",
      mobileImage: null,
      webImage: null,
    },
    {
      id: 3,
      position: "Position #3",
      text: "Text",
      mobileImage: null,
      webImage: null,
    },
  ]);

  const handleImageUpload = (
    event: React.ChangeEvent<HTMLInputElement>,
    type: "mobile" | "web" | "position",
    positionId?: number
  ) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = () => {
      const result = reader.result as string;

      if (type === "mobile") {
        setMobileImage(file);
        setMobileImagePreview(result);
      } else if (type === "web") {
        setWebImage(file);
        setWebImagePreview(result);
      } else if (type === "position" && positionId) {
        setPositions((prev) =>
          prev.map((pos) =>
            pos.id === positionId
              ? {
                  ...pos,
                  mobileImage: file,
                  mobileImagePreview: result,
                  webImage: file,
                  webImagePreview: result,
                }
              : pos
          )
        );
      }
    };
    reader.readAsDataURL(file);
  };

  const handleAddPosition = () => {
    if (!position.trim() || !text.trim()) return;

    const newPosition: NoticePosition = {
      id: Date.now(),
      position: position,
      text: text,
      mobileImage: mobileImage,
      webImage: webImage,
      mobileImagePreview: mobileImagePreview,
      webImagePreview: webImagePreview,
    };

    setPositions((prev) => [...prev, newPosition]);

    // Reset form
    setPosition("");
    setText("");
    setMobileImage(null);
    setWebImage(null);
    setMobileImagePreview("");
    setWebImagePreview("");
  };

  const handleDeletePosition = (id: number) => {
    setPositions((prev) => prev.filter((pos) => pos.id !== id));
  };

  const handleEditPosition = (id: number) => {
    // Find the position to edit
    const positionToEdit = positions.find((pos) => pos.id === id);
    if (positionToEdit) {
      setPosition(positionToEdit.position);
      setText(positionToEdit.text);
      setMobileImage(positionToEdit.mobileImage || null);
      setWebImage(positionToEdit.webImage || null);
      setMobileImagePreview(positionToEdit.mobileImagePreview || "");
      setWebImagePreview(positionToEdit.webImagePreview || "");

      // Remove the position from the list so it can be re-added with updates
      handleDeletePosition(id);
    }
  };

  return (
    <div className="flex flex-col mt-4 space-y-6">
      {/* Form Section */}
      <div className="flex flex-col space-y-4">
        <div className="flex flex-col space-y-4 max-w-md">
          <div className="flex flex-col space-y-2">
            <label className="text-white text-sm font-medium">
              Enter Position
            </label>
            <Input
              type="text"
              placeholder="#1"
              value={position}
              onChange={(e) => setPosition(e.target.value)}
              className="bg-white/5 border border-white/20 rounded-lg text-white placeholder:text-gray-400"
            />
          </div>

          <div className="flex flex-col space-y-2">
            <label className="text-white text-sm font-medium">Enter Text</label>
            <Textarea
              placeholder="Exam Result will publish on 17-08-25"
              value={text}
              onChange={(e) => setText(e.target.value)}
              className="bg-white/5 border border-white/20 rounded-lg text-white placeholder:text-gray-400 min-h-20"
            />
          </div>
        </div>

        {/* Mobile and Web Image Upload Section */}
        <div className="flex space-x-6">
          {/* Mobile Section */}
          <div className="flex flex-col space-y-2">
            <h3 className="text-white text-lg font-medium">Mobile</h3>
            <div className="relative">
              <div className="w-48 h-32 bg-white/5 border border-white/20 rounded-lg flex items-center justify-center cursor-pointer hover:bg-white/10 transition-colors">
                <input
                  type="file"
                  accept="image/*"
                  onChange={(e) => handleImageUpload(e, "mobile")}
                  className="absolute inset-0 opacity-0 cursor-pointer"
                />
                {mobileImagePreview ? (
                  <img
                    src={mobileImagePreview}
                    alt="Mobile preview"
                    className="w-full h-full object-cover rounded-lg"
                  />
                ) : (
                  <ImageIcon className="w-12 h-12 text-gray-400" />
                )}
              </div>
              <Button size="sm" variant="secondary" className="mt-2 w-full">
                <Edit className="w-4 h-4 mr-1" />
                Edit
              </Button>
            </div>
          </div>

          {/* Web Section */}
          <div className="flex flex-col space-y-2">
            <h3 className="text-white text-lg font-medium">Web</h3>
            <div className="relative">
              <div className="w-48 h-32 bg-white/5 border border-white/20 rounded-lg flex items-center justify-center cursor-pointer hover:bg-white/10 transition-colors">
                <input
                  type="file"
                  accept="image/*"
                  onChange={(e) => handleImageUpload(e, "web")}
                  className="absolute inset-0 opacity-0 cursor-pointer"
                />
                {webImagePreview ? (
                  <img
                    src={webImagePreview}
                    alt="Web preview"
                    className="w-full h-full object-cover rounded-lg"
                  />
                ) : (
                  <ImageIcon className="w-12 h-12 text-gray-400" />
                )}
              </div>
              <Button size="sm" variant="secondary" className="mt-2 w-full">
                <Edit className="w-4 h-4 mr-1" />
                Edit
              </Button>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex space-x-3 justify-end max-w-2xl">
          <Button
            variant="outline"
            className="bg-transparent border-white/20 text-white hover:bg-white/5"
          >
            Cancel
          </Button>
          <Button
            onClick={handleAddPosition}
            className="bg-btn-tertiary hover:bg-btn-tertiary-hover text-btn-tertiary-text"
          >
            Add
          </Button>
        </div>
      </div>

      {/* Notice Board Images Section */}
      <div className="space-y-4">
        <div>
          <h2 className="text-white text-xl font-semibold">
            Notice Board Images
          </h2>
          <p className="text-gray-400 text-sm">
            Manage and monitor all Notice Board Images
          </p>
        </div>

        {/* Dynamic Positions List */}
        <div className="space-y-4">
          {positions.map((pos) => (
            <div
              key={pos.id}
              className="bg-white/5 border border-white/20 rounded-lg p-4"
            >
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-white text-lg font-medium">
                  {pos.position}
                </h3>
                <div className="flex space-x-2">
                  <Button
                    size="sm"
                    variant="secondary"
                    onClick={() => handleEditPosition(pos.id)}
                  >
                    <Edit className="w-4 h-4 mr-1" />
                    Edit
                  </Button>
                  <Button
                    size="sm"
                    variant="destructive"
                    onClick={() => handleDeletePosition(pos.id)}
                  >
                    <Trash2 className="w-4 h-4 mr-1" />
                    Delete
                  </Button>
                </div>
              </div>

              <div className="flex space-x-6">
                {/* Mobile Image */}
                <div className="flex flex-col space-y-2">
                  <span className="text-gray-400 text-sm">{pos.text}</span>
                  <div className="relative">
                    <div className="w-32 h-24 bg-white/5 border border-white/20 rounded-lg flex items-center justify-center cursor-pointer hover:bg-white/10 transition-colors">
                      <input
                        type="file"
                        accept="image/*"
                        onChange={(e) =>
                          handleImageUpload(e, "position", pos.id)
                        }
                        className="absolute inset-0 opacity-0 cursor-pointer"
                      />
                      {pos.mobileImagePreview ? (
                        <img
                          src={pos.mobileImagePreview}
                          alt="Position mobile"
                          className="w-full h-full object-cover rounded-lg"
                        />
                      ) : (
                        <ImageIcon className="w-8 h-8 text-gray-400" />
                      )}
                    </div>
                  </div>
                </div>

                {/* Web Image */}
                <div className="flex flex-col space-y-2">
                  <span className="text-transparent text-sm">.</span>{" "}
                  {/* Spacer */}
                  <div className="relative">
                    <div className="w-32 h-24 bg-white/5 border border-white/20 rounded-lg flex items-center justify-center cursor-pointer hover:bg-white/10 transition-colors">
                      <input
                        type="file"
                        accept="image/*"
                        onChange={(e) =>
                          handleImageUpload(e, "position", pos.id)
                        }
                        className="absolute inset-0 opacity-0 cursor-pointer"
                      />
                      {pos.webImagePreview ? (
                        <img
                          src={pos.webImagePreview}
                          alt="Position web"
                          className="w-full h-full object-cover rounded-lg"
                        />
                      ) : (
                        <ImageIcon className="w-8 h-8 text-gray-400" />
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
