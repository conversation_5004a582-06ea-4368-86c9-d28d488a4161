import { Sidebar } from "@/components/constant/side-bar";
import Header from "@/components/constant/header";
import React, { ReactNode } from "react";

interface MainLayoutProps {
  children: ReactNode;
}

export default function MainLayout({ children }: MainLayoutProps) {
  return (
    <div className="flex h-screen bg-[#0E0E0E] text-white">
      {/* Sidebar */}
      <Sidebar />

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Header */}
        <Header title="Dashboard" />

        {/* Page Content */}
        <main className="flex-1 mt-14 p-6 overflow-y-auto scrollbar-hide">
          <div className="full-width-container">{children}</div>
        </main>
      </div>
    </div>
  );
}
