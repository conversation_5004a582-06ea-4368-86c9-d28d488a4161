// API Configuration for external Spring Boot services
export const API_CONFIG = {
  // Spring Boot Backend Base URLs
  SPRING_BOOT_BASE_URL: process.env.SPRING_BOOT_API_URL || 'http://localhost:8080',
  
  // External API Endpoints that our Next.js APIs call
  EXTERNAL_APIS: {
    // Authentication Service
    AUTH_SERVICE: {
      baseUrl: process.env.AUTH_SERVICE_URL || 'http://localhost:8080/auth',
      endpoints: {
        login: '/api/v1/auth/login',
        logout: '/api/v1/auth/logout', 
        refresh: '/api/v1/auth/refresh-token',
        validate: '/api/v1/auth/validate-token',
      }
    },
    
    // User Management Service
    USER_SERVICE: {
      baseUrl: process.env.USER_SERVICE_URL || 'http://localhost:8080/users',
      endpoints: {
        getUsers: '/api/v1/users',
        createUser: '/api/v1/users',
        getUserById: '/api/v1/users/{id}',
        updateUser: '/api/v1/users/{id}',
        deleteUser: '/api/v1/users/{id}',
        getUserProfile: '/api/v1/users/{id}/profile',
      }
    },
    
    // Content Management Service
    CONTENT_SERVICE: {
      baseUrl: process.env.CONTENT_SERVICE_URL || 'http://localhost:8080/content',
      endpoints: {
        getTracks: '/api/v1/tracks',
        createTrack: '/api/v1/tracks',
        getTrackById: '/api/v1/tracks/{id}',
        updateTrack: '/api/v1/tracks/{id}',
        deleteTrack: '/api/v1/tracks/{id}',
        getSubjects: '/api/v1/subjects',
        createSubject: '/api/v1/subjects',
        getChapters: '/api/v1/chapters',
        createChapter: '/api/v1/chapters',
      }
    },
    
    // Finance Service
    FINANCE_SERVICE: {
      baseUrl: process.env.FINANCE_SERVICE_URL || 'http://localhost:8080/finance',
      endpoints: {
        getTransactions: '/api/v1/transactions',
        createTransaction: '/api/v1/transactions',
        getFinancialSummary: '/api/v1/finance/summary',
        getPayments: '/api/v1/payments',
        processPayment: '/api/v1/payments/process',
      }
    },
    
    // Analytics Service
    ANALYTICS_SERVICE: {
      baseUrl: process.env.ANALYTICS_SERVICE_URL || 'http://localhost:8080/analytics',
      endpoints: {
        getDashboardStats: '/api/v1/analytics/dashboard',
        getUserAnalytics: '/api/v1/analytics/users',
        getContentAnalytics: '/api/v1/analytics/content',
        getRevenueAnalytics: '/api/v1/analytics/revenue',
      }
    },
    
    // Notification Service
    NOTIFICATION_SERVICE: {
      baseUrl: process.env.NOTIFICATION_SERVICE_URL || 'http://localhost:8080/notifications',
      endpoints: {
        sendEmail: '/api/v1/notifications/email',
        sendSMS: '/api/v1/notifications/sms',
        getNotificationHistory: '/api/v1/notifications/history',
      }
    },
    
    // File Storage Service
    FILE_SERVICE: {
      baseUrl: process.env.FILE_SERVICE_URL || 'http://localhost:8080/files',
      endpoints: {
        uploadFile: '/api/v1/files/upload',
        getFile: '/api/v1/files/{id}',
        deleteFile: '/api/v1/files/{id}',
        getFileMetadata: '/api/v1/files/{id}/metadata',
      }
    },
    
    // Test Results Service
    RESULTS_SERVICE: {
      baseUrl: process.env.RESULTS_SERVICE_URL || 'http://localhost:8080/results',
      endpoints: {
        getTestResults: '/api/v1/test-results',
        createTestResult: '/api/v1/test-results',
        getResultById: '/api/v1/test-results/{id}',
        publishResults: '/api/v1/test-results/{id}/publish',
        getPrizeCategories: '/api/v1/prize-categories',
      }
    },
    
    // Sponsor Management Service
    SPONSOR_SERVICE: {
      baseUrl: process.env.SPONSOR_SERVICE_URL || 'http://localhost:8080/sponsors',
      endpoints: {
        getSponsors: '/api/v1/sponsors',
        createSponsor: '/api/v1/sponsors',
        getSponsorById: '/api/v1/sponsors/{id}',
        updateSponsor: '/api/v1/sponsors/{id}',
        getAdvertisements: '/api/v1/advertisements',
        createAdvertisement: '/api/v1/advertisements',
      }
    }
  }
};

// Helper function to build full URL
export const buildApiUrl = (service: keyof typeof API_CONFIG.EXTERNAL_APIS, endpoint: string, params?: Record<string, string>) => {
  const serviceConfig = API_CONFIG.EXTERNAL_APIS[service];
  let url = `${serviceConfig.baseUrl}${endpoint}`;
  
  // Replace path parameters
  if (params) {
    Object.entries(params).forEach(([key, value]) => {
      url = url.replace(`{${key}}`, value);
    });
  }
  
  return url;
};

// API Integration Documentation
export const API_INTEGRATION_DOCS = {
  description: `
    This Next.js application serves as a frontend API layer that integrates with multiple Spring Boot microservices.
    Each frontend endpoint calls corresponding backend services to provide a unified API experience.
  `,
  architecture: {
    pattern: 'API Gateway Pattern',
    description: 'Next.js acts as an API Gateway, routing requests to appropriate Spring Boot microservices',
    benefits: [
      'Unified API interface for frontend clients',
      'Authentication and authorization at gateway level', 
      'Request/response transformation',
      'Error handling and logging',
      'Rate limiting and caching'
    ]
  },
  services: Object.keys(API_CONFIG.EXTERNAL_APIS),
  authFlow: {
    description: 'Authentication tokens are validated at the Next.js layer and forwarded to Spring Boot services',
    steps: [
      '1. Client authenticates with Next.js /api/auth endpoint',
      '2. Next.js forwards credentials to Spring Boot Auth Service',
      '3. Spring Boot returns JWT token',
      '4. Next.js validates and returns token to client',
      '5. Subsequent requests include token in Authorization header',
      '6. Next.js validates token and forwards to appropriate Spring Boot service'
    ]
  }
};
