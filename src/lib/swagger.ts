import swaggerJSDoc from 'swagger-jsdoc';

const swaggerDefinition = {
  openapi: '3.0.0',
  info: {
    title: 'Admin Panel API',
    version: '1.0.0',
    description: 'Comprehensive API documentation for the Admin Panel application with proper segregation and detailed endpoints',
    contact: {
      name: 'Admin Panel Team',
      email: '<EMAIL>',
    },
    license: {
      name: 'MIT',
      url: 'https://opensource.org/licenses/MIT',
    },
  },
  servers: [
    {
      url: 'http://localhost:3000/api',
      description: 'Development server',
    },
    {
      url: 'https://your-production-domain.com/api',
      description: 'Production server',
    },
  ],
  components: {
    securitySchemes: {
      bearerAuth: {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        description: 'JWT token for authentication',
      },
      apiKey: {
        type: 'apiKey',
        in: 'header',
        name: 'X-API-Key',
        description: 'API key for service-to-service authentication',
      },
    },
    schemas: {
      Error: {
        type: 'object',
        properties: {
          success: {
            type: 'boolean',
            example: false,
          },
          message: {
            type: 'string',
            example: 'An error occurred',
          },
          error: {
            type: 'string',
            example: 'Detailed error message',
          },
          statusCode: {
            type: 'integer',
            example: 400,
          },
        },
        required: ['success', 'message'],
      },
      Success: {
        type: 'object',
        properties: {
          success: {
            type: 'boolean',
            example: true,
          },
          message: {
            type: 'string',
            example: 'Operation completed successfully',
          },
          data: {
            type: 'object',
            description: 'Response data',
          },
        },
        required: ['success', 'message'],
      },
      PaginationMeta: {
        type: 'object',
        properties: {
          page: {
            type: 'integer',
            example: 1,
            description: 'Current page number',
          },
          limit: {
            type: 'integer',
            example: 10,
            description: 'Number of items per page',
          },
          total: {
            type: 'integer',
            example: 100,
            description: 'Total number of items',
          },
          totalPages: {
            type: 'integer',
            example: 10,
            description: 'Total number of pages',
          },
        },
        required: ['page', 'limit', 'total', 'totalPages'],
      },
      PaginatedResponse: {
        type: 'object',
        properties: {
          success: {
            type: 'boolean',
            example: true,
          },
          message: {
            type: 'string',
            example: 'Data retrieved successfully',
          },
          data: {
            type: 'array',
            items: {
              type: 'object',
            },
          },
          meta: {
            $ref: '#/components/schemas/PaginationMeta',
          },
        },
        required: ['success', 'message', 'data', 'meta'],
      },
    },
  },
  tags: [
    {
      name: 'Authentication',
      description: 'Authentication and authorization endpoints',
    },
    {
      name: 'Users',
      description: 'User management endpoints for subscribers and LE users',
    },
    {
      name: 'Content',
      description: 'Content management endpoints for tracks, subjects, and chapters',
    },
    {
      name: 'Finance',
      description: 'Financial data and transaction management',
    },
    {
      name: 'Sponsors & Ads',
      description: 'Sponsor and advertisement management',
    },
    {
      name: 'Results',
      description: 'Test results and analytics endpoints',
    },
    {
      name: 'Master Data',
      description: 'Master data management (timetables, prizes, tracks)',
    },
    {
      name: 'Dashboard',
      description: 'Dashboard statistics and overview data',
    },
    {
      name: 'Audit Log',
      description: 'System audit and activity logging',
    },
  ],
};

const options = {
  definition: swaggerDefinition,
  apis: [
    './src/app/api/**/*.ts',
    './src/app/api/**/*.js',
    './src/models/*.ts',
  ],
};

export const swaggerSpec = swaggerJSDoc(options);
export default swaggerSpec;
