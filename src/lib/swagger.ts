import swaggerJSD<PERSON> from "swagger-jsdoc";
import { API_CONFIG, API_INTEGRATION_DOCS } from "./api-config";

const swaggerDefinition = {
  openapi: "3.0.0",
  info: {
    title: "Admin Panel API Gateway",
    version: "1.0.0",
    description: `
# Admin Panel API Gateway

${API_INTEGRATION_DOCS.description}

## Architecture
- **Pattern**: ${API_INTEGRATION_DOCS.architecture.pattern}
- **Description**: ${API_INTEGRATION_DOCS.architecture.description}

## Benefits
${API_INTEGRATION_DOCS.architecture.benefits
  .map((benefit) => `- ${benefit}`)
  .join("\n")}

## Authentication Flow
${API_INTEGRATION_DOCS.authFlow.steps.map((step) => `${step}`).join("\n")}

## Integrated Spring Boot Services
${Object.entries(API_CONFIG.EXTERNAL_APIS)
  .map(([key, service]) => `- **${key}**: ${service.baseUrl}`)
  .join("\n")}

---

This documentation shows both the Next.js API endpoints and the Spring Boot services they integrate with.
    `,
    contact: {
      name: "Admin Panel Team",
      email: "<EMAIL>",
    },
    license: {
      name: "MIT",
      url: "https://opensource.org/licenses/MIT",
    },
  },
  servers: [
    {
      url: "http://localhost:3000/api",
      description: "Next.js API Gateway (Development)",
    },
    {
      url: "https://your-production-domain.com/api",
      description: "Next.js API Gateway (Production)",
    },
    {
      url: API_CONFIG.EXTERNAL_APIS.AUTH_SERVICE.baseUrl,
      description: "Spring Boot Auth Service",
    },
    {
      url: API_CONFIG.EXTERNAL_APIS.USER_SERVICE.baseUrl,
      description: "Spring Boot User Service",
    },
    {
      url: API_CONFIG.EXTERNAL_APIS.CONTENT_SERVICE.baseUrl,
      description: "Spring Boot Content Service",
    },
    {
      url: API_CONFIG.EXTERNAL_APIS.FINANCE_SERVICE.baseUrl,
      description: "Spring Boot Finance Service",
    },
    {
      url: API_CONFIG.EXTERNAL_APIS.ANALYTICS_SERVICE.baseUrl,
      description: "Spring Boot Analytics Service",
    },
  ],
  components: {
    securitySchemes: {
      bearerAuth: {
        type: "http",
        scheme: "bearer",
        bearerFormat: "JWT",
        description: "JWT token for authentication",
      },
      apiKey: {
        type: "apiKey",
        in: "header",
        name: "X-API-Key",
        description: "API key for service-to-service authentication",
      },
    },
    schemas: {
      Error: {
        type: "object",
        properties: {
          success: {
            type: "boolean",
            example: false,
          },
          message: {
            type: "string",
            example: "An error occurred",
          },
          error: {
            type: "string",
            example: "Detailed error message",
          },
          statusCode: {
            type: "integer",
            example: 400,
          },
        },
        required: ["success", "message"],
      },
      Success: {
        type: "object",
        properties: {
          success: {
            type: "boolean",
            example: true,
          },
          message: {
            type: "string",
            example: "Operation completed successfully",
          },
          data: {
            type: "object",
            description: "Response data",
          },
        },
        required: ["success", "message"],
      },
      PaginationMeta: {
        type: "object",
        properties: {
          page: {
            type: "integer",
            example: 1,
            description: "Current page number",
          },
          limit: {
            type: "integer",
            example: 10,
            description: "Number of items per page",
          },
          total: {
            type: "integer",
            example: 100,
            description: "Total number of items",
          },
          totalPages: {
            type: "integer",
            example: 10,
            description: "Total number of pages",
          },
        },
        required: ["page", "limit", "total", "totalPages"],
      },
      PaginatedResponse: {
        type: "object",
        properties: {
          success: {
            type: "boolean",
            example: true,
          },
          message: {
            type: "string",
            example: "Data retrieved successfully",
          },
          data: {
            type: "array",
            items: {
              type: "object",
            },
          },
          meta: {
            $ref: "#/components/schemas/PaginationMeta",
          },
        },
        required: ["success", "message", "data", "meta"],
      },
    },
  },
  tags: [
    {
      name: "Authentication",
      description:
        "Authentication and authorization endpoints (integrates with Spring Boot Auth Service)",
      externalDocs: {
        description: "Spring Boot Auth Service",
        url: API_CONFIG.EXTERNAL_APIS.AUTH_SERVICE.baseUrl,
      },
    },
    {
      name: "Users",
      description:
        "User management endpoints for subscribers and LE users (integrates with Spring Boot User Service)",
      externalDocs: {
        description: "Spring Boot User Service",
        url: API_CONFIG.EXTERNAL_APIS.USER_SERVICE.baseUrl,
      },
    },
    {
      name: "Content",
      description:
        "Content management endpoints for tracks, subjects, and chapters (integrates with Spring Boot Content Service)",
      externalDocs: {
        description: "Spring Boot Content Service",
        url: API_CONFIG.EXTERNAL_APIS.CONTENT_SERVICE.baseUrl,
      },
    },
    {
      name: "Finance",
      description:
        "Financial data and transaction management (integrates with Spring Boot Finance Service)",
      externalDocs: {
        description: "Spring Boot Finance Service",
        url: API_CONFIG.EXTERNAL_APIS.FINANCE_SERVICE.baseUrl,
      },
    },
    {
      name: "Sponsors & Ads",
      description:
        "Sponsor and advertisement management (integrates with Spring Boot Sponsor Service)",
      externalDocs: {
        description: "Spring Boot Sponsor Service",
        url: API_CONFIG.EXTERNAL_APIS.SPONSOR_SERVICE.baseUrl,
      },
    },
    {
      name: "Results",
      description:
        "Test results and analytics endpoints (integrates with Spring Boot Results Service)",
      externalDocs: {
        description: "Spring Boot Results Service",
        url: API_CONFIG.EXTERNAL_APIS.RESULTS_SERVICE.baseUrl,
      },
    },
    {
      name: "Master Data",
      description: "Master data management (timetables, prizes, tracks)",
    },
    {
      name: "Dashboard",
      description:
        "Dashboard statistics and overview data (integrates with Spring Boot Analytics Service)",
      externalDocs: {
        description: "Spring Boot Analytics Service",
        url: API_CONFIG.EXTERNAL_APIS.ANALYTICS_SERVICE.baseUrl,
      },
    },
    {
      name: "Audit Log",
      description: "System audit and activity logging",
    },
    {
      name: "External API Integration",
      description:
        "Documentation of Spring Boot APIs that this gateway integrates with",
    },
  ],
};

const options = {
  definition: swaggerDefinition,
  apis: ["./src/app/api/**/*.ts", "./src/app/api/**/*.js", "./src/models/*.ts"],
};

export const swaggerSpec = swaggerJSDoc(options);
export default swaggerSpec;
