# Spring Boot API Integration Documentation

This document details how the Next.js API Gateway integrates with Spring Boot microservices, showing the complete mapping between frontend and backend APIs.

## 🏗️ Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────────────┐
│   Frontend      │    │   Next.js API   │    │   Spring Boot Services  │
│   Application   │───▶│     Gateway     │───▶│                         │
│                 │    │                 │    │  ┌─────────────────┐    │
└─────────────────┘    └─────────────────┘    │  │ Auth Service    │    │
                                              │  │ User Service    │    │
                                              │  │ Content Service │    │
                                              │  │ Finance Service │    │
                                              │  │ Analytics Svc   │    │
                                              │  │ File Service    │    │
                                              │  │ Results Service │    │
                                              │  │ Sponsor Service │    │
                                              │  │ Notification    │    │
                                              │  └─────────────────┘    │
                                              └─────────────────────────┘
```

## 🔗 API Mapping

### Authentication Service Integration

| Next.js Gateway Endpoint | Spring Boot Endpoint | Description |
|--------------------------|----------------------|-------------|
| `POST /api/auth` | `POST /api/v1/auth/login` | User authentication |
| `POST /api/auth/logout` | `POST /api/v1/auth/logout` | User logout |
| `POST /api/auth/refresh` | `POST /api/v1/auth/refresh-token` | Token refresh |

**Spring Boot Service URL**: `http://localhost:8080/auth`
**Swagger Documentation**: `http://localhost:8080/auth/swagger-ui.html`

### User Management Service Integration

| Next.js Gateway Endpoint | Spring Boot Endpoint | Description |
|--------------------------|----------------------|-------------|
| `GET /api/users/subscribers` | `GET /api/v1/users` | Get all users with pagination |
| `POST /api/users/subscribers` | `POST /api/v1/users` | Create new user |
| `GET /api/users/subscribers/{id}` | `GET /api/v1/users/{id}` | Get user by ID |
| `PUT /api/users/subscribers/{id}` | `PUT /api/v1/users/{id}` | Update user |
| `DELETE /api/users/subscribers/{id}` | `DELETE /api/v1/users/{id}` | Delete user |

**Spring Boot Service URL**: `http://localhost:8080/users`
**Swagger Documentation**: `http://localhost:8080/users/swagger-ui.html`

### Content Management Service Integration

| Next.js Gateway Endpoint | Spring Boot Endpoint | Description |
|--------------------------|----------------------|-------------|
| `GET /api/content/tracks` | `GET /api/v1/tracks` | Get all learning tracks |
| `POST /api/content/tracks` | `POST /api/v1/tracks` | Create new track |
| `GET /api/content/tracks/{id}` | `GET /api/v1/tracks/{id}` | Get track by ID |
| `PUT /api/content/tracks/{id}` | `PUT /api/v1/tracks/{id}` | Update track |
| `DELETE /api/content/tracks/{id}` | `DELETE /api/v1/tracks/{id}` | Delete track |

**Spring Boot Service URL**: `http://localhost:8080/content`
**Swagger Documentation**: `http://localhost:8080/content/swagger-ui.html`

### Analytics Service Integration

| Next.js Gateway Endpoint | Spring Boot Endpoint | Description |
|--------------------------|----------------------|-------------|
| `GET /api/dashboard/stats` | `GET /api/v1/analytics/dashboard` | Dashboard statistics |
| `GET /api/analytics/users` | `GET /api/v1/analytics/users` | User analytics |
| `GET /api/analytics/content` | `GET /api/v1/analytics/content` | Content analytics |
| `GET /api/analytics/revenue` | `GET /api/v1/analytics/revenue` | Revenue analytics |

**Spring Boot Service URL**: `http://localhost:8080/analytics`
**Swagger Documentation**: `http://localhost:8080/analytics/swagger-ui.html`

### File Service Integration

| Next.js Gateway Endpoint | Spring Boot Endpoint | Description |
|--------------------------|----------------------|-------------|
| `POST /api/upload` | `POST /api/v1/files/upload` | Upload files |
| `GET /api/upload` | `GET /api/v1/files/guidelines` | Get upload guidelines |
| `GET /api/files/{id}` | `GET /api/v1/files/{id}` | Get file by ID |
| `DELETE /api/files/{id}` | `DELETE /api/v1/files/{id}` | Delete file |

**Spring Boot Service URL**: `http://localhost:8080/files`
**Swagger Documentation**: `http://localhost:8080/files/swagger-ui.html`

## 🔧 Implementation Examples

### Authentication Flow

```typescript
// Next.js Gateway Implementation
export async function POST(request: NextRequest) {
  const body = await request.json();
  
  // Forward to Spring Boot Auth Service
  const response = await fetch(`${API_CONFIG.EXTERNAL_APIS.AUTH_SERVICE.baseUrl}/api/v1/auth/login`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(body)
  });
  
  const authResult = await response.json();
  
  // Transform and return response
  return NextResponse.json({
    success: true,
    message: 'Login successful',
    data: authResult
  });
}
```

### User Management Flow

```typescript
// Next.js Gateway Implementation
export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  
  // Forward to Spring Boot User Service
  const response = await fetch(`${API_CONFIG.EXTERNAL_APIS.USER_SERVICE.baseUrl}/api/v1/users?${searchParams}`);
  const userData = await response.json();
  
  // Transform and return response
  return NextResponse.json({
    success: true,
    message: 'Users retrieved successfully',
    data: userData.content,
    meta: {
      page: userData.number + 1,
      limit: userData.size,
      total: userData.totalElements,
      totalPages: userData.totalPages
    }
  });
}
```

## 🛠️ Configuration

### Environment Variables

```bash
# Spring Boot Service URLs
SPRING_BOOT_API_URL=http://localhost:8080
AUTH_SERVICE_URL=http://localhost:8080/auth
USER_SERVICE_URL=http://localhost:8080/users
CONTENT_SERVICE_URL=http://localhost:8080/content
FINANCE_SERVICE_URL=http://localhost:8080/finance
ANALYTICS_SERVICE_URL=http://localhost:8080/analytics
NOTIFICATION_SERVICE_URL=http://localhost:8080/notifications
FILE_SERVICE_URL=http://localhost:8080/files
RESULTS_SERVICE_URL=http://localhost:8080/results
SPONSOR_SERVICE_URL=http://localhost:8080/sponsors
```

### API Configuration

The API configuration is centralized in `src/lib/api-config.ts`:

```typescript
export const API_CONFIG = {
  SPRING_BOOT_BASE_URL: process.env.SPRING_BOOT_API_URL || 'http://localhost:8080',
  EXTERNAL_APIS: {
    AUTH_SERVICE: {
      baseUrl: process.env.AUTH_SERVICE_URL || 'http://localhost:8080/auth',
      endpoints: {
        login: '/api/v1/auth/login',
        logout: '/api/v1/auth/logout',
        refresh: '/api/v1/auth/refresh-token',
      }
    },
    // ... other services
  }
};
```

## 📊 Monitoring & Health Checks

### Health Check Endpoints

Each Spring Boot service provides health check endpoints:

- **Auth Service**: `http://localhost:8080/auth/actuator/health`
- **User Service**: `http://localhost:8080/users/actuator/health`
- **Content Service**: `http://localhost:8080/content/actuator/health`
- **Finance Service**: `http://localhost:8080/finance/actuator/health`
- **Analytics Service**: `http://localhost:8080/analytics/actuator/health`

### Gateway Health Check

The Next.js gateway can check all services:

```bash
curl http://localhost:3000/api/external-apis?includeEndpoints=false
```

## 🚀 Getting Started

1. **Start Spring Boot Services**:
   ```bash
   # Start all Spring Boot microservices on their respective ports
   java -jar auth-service.jar --server.port=8080
   java -jar user-service.jar --server.port=8081
   java -jar content-service.jar --server.port=8082
   # ... etc
   ```

2. **Configure Environment Variables**:
   ```bash
   cp .env.example .env
   # Update service URLs in .env file
   ```

3. **Start Next.js Gateway**:
   ```bash
   npm run dev
   ```

4. **Access Documentation**:
   - **Gateway Docs**: `http://localhost:3000/api/docs`
   - **External APIs**: `http://localhost:3000/api/external-apis`
   - **Spring Boot Docs**: Individual service Swagger URLs

## 🔍 Testing Integration

### Test Gateway → Spring Boot Flow

```bash
# Test authentication flow
curl -X POST http://localhost:3000/api/auth \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "password"}'

# Test user management flow
curl -X GET "http://localhost:3000/api/users/subscribers?page=1&limit=10" \
  -H "Authorization: Bearer <token>"
```

### Verify Spring Boot Direct Access

```bash
# Test Spring Boot service directly
curl -X POST http://localhost:8080/auth/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "password"}'
```

This integration provides a seamless experience where the Next.js gateway handles all client interactions while leveraging the power of Spring Boot microservices for business logic and data management.
