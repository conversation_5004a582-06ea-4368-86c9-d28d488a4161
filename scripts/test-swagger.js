const swaggerJSDoc = require('swagger-jsdoc');

const swaggerDefinition = {
  openapi: '3.0.0',
  info: {
    title: 'Admin Panel API',
    version: '1.0.0',
    description: 'Comprehensive API documentation for the Admin Panel application with proper segregation and detailed endpoints',
  },
  servers: [
    {
      url: 'http://localhost:3000/api',
      description: 'Development server',
    },
  ],
};

const options = {
  definition: swaggerDefinition,
  apis: [
    './src/app/api/**/*.ts',
    './src/models/*.ts',
  ],
};

try {
  const swaggerSpec = swaggerJSDoc(options);
  console.log('✅ Swagger configuration is valid');
  console.log(`📊 Found ${Object.keys(swaggerSpec.paths || {}).length} API paths`);
  console.log(`🏷️  Found ${Object.keys(swaggerSpec.components?.schemas || {}).length} schemas`);
  
  // List all paths
  if (swaggerSpec.paths) {
    console.log('\n📋 API Endpoints:');
    Object.keys(swaggerSpec.paths).forEach(path => {
      const methods = Object.keys(swaggerSpec.paths[path]);
      console.log(`  ${path}: ${methods.join(', ').toUpperCase()}`);
    });
  }
  
  // List all schemas
  if (swaggerSpec.components?.schemas) {
    console.log('\n📝 Schemas:');
    Object.keys(swaggerSpec.components.schemas).forEach(schema => {
      console.log(`  - ${schema}`);
    });
  }
  
} catch (error) {
  console.error('❌ Swagger configuration error:', error.message);
  process.exit(1);
}
